<?php
// --- PHP Code Block (Database Connection, Search, Pagination, Stats, Activity Fetching) ---
ini_set('display_errors', 0); // Don't display errors directly to users
ini_set('log_errors', 1); // Log errors to the server's error log
error_reporting(E_ALL); // Report all errors for logging purposes

// Start the session at the beginning of the file
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php'); // Redirect to login page if not logged in
    exit;
}

// Set a flag to show welcome message if this is the first page load after login
$show_welcome_message = false;
if (!isset($_SESSION['welcome_shown'])) {
    $_SESSION['welcome_shown'] = true;
    $show_welcome_message = true;
}

require_once 'config/database.php'; // Ensure this path is correct

// Check database connection
if (!$conn) {
    error_log("Database connection failed in index.php: " . mysqli_connect_error());
    $db_connection_error = "Error connecting to the database. Please try again later or contact support.";
} else {
    $db_connection_error = null;
    mysqli_set_charset($conn, "utf8mb4"); // Ensure UTF-8
}

// --- View Parameters ---
$current_view = 'month'; // For stats comparison logic
$activities_view = isset($_GET['view']) ? trim($_GET['view']) : 'all'; // 'all' or 'project'

// --- Pagination Configuration (Read GET params ONCE) ---
$results_per_page_options = [5, 10, 20, 50, 100];
$default_results_per_page = 10;
$current_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1; // Initial requested page
$results_per_page = isset($_GET['limit']) ? (int)$_GET['limit'] : $default_results_per_page;
if (!in_array($results_per_page, $results_per_page_options)) {
    $results_per_page = $default_results_per_page;
}

// --- Search Term (Read ONCE) ---
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';

// --- Filter Columns Definition ---
$filter_columns = ['indicator', 'sector', 'municipality', 'barangay', 'project'];

// --- Initialize variables used by BOTH views' pagination/data sections ---
$total_pages = 1; // Default to 1 page
$offset = 0;
$total_items_for_pagination = 0; // Will hold either total_municipalities or total_activities


// --- Project Activities View Data ---
$projects = [];
$project_counts = [];
$project_counts_error = null;
$total_municipalities = 0; // Specific count for project view

if ($activities_view === 'project' && !$db_connection_error) {
    try {
        // Get project columns from tblproject
        $project_columns_sql = "SHOW COLUMNS FROM tblproject WHERE Field NOT IN ('id', 'municity')";
        $project_columns_result = mysqli_query($conn, $project_columns_sql);
        if (!$project_columns_result) throw new Exception("Error fetching project columns: " . mysqli_error($conn));
        while ($column = mysqli_fetch_assoc($project_columns_result)) { $projects[] = $column['Field']; }
        mysqli_free_result($project_columns_result);

        // --- Search specific to Project View (by municity) ---
        $project_where_clause = "";
        $project_search_params = [];
        $project_search_types = "";
        if (!empty($search_term)) {
            $project_where_clause = " WHERE municity LIKE ? ";
            $project_search_params[] = "%$search_term%";
            $project_search_types = "s";
        }

        // Count total municipalities for pagination (with search applied)
        $count_municipalities_sql = "SELECT COUNT(DISTINCT municity) as total FROM tblproject $project_where_clause";
        $stmt_proj_count = mysqli_prepare($conn, $count_municipalities_sql);
        if (!$stmt_proj_count) throw new Exception("Error preparing project count query: " . mysqli_error($conn));
        if (!empty($project_search_params)) mysqli_stmt_bind_param($stmt_proj_count, $project_search_types, ...$project_search_params);
        if (!mysqli_stmt_execute($stmt_proj_count)) throw new Exception("Error executing project count query: " . mysqli_stmt_error($stmt_proj_count));
        $count_result = mysqli_stmt_get_result($stmt_proj_count);
        if (!$count_result) throw new Exception("Error getting project count result: " . mysqli_error($conn));
        $count_row = mysqli_fetch_assoc($count_result);
        $total_municipalities = (int)$count_row['total']; // <<<< THIS IS THE TOTAL FOR PROJECT VIEW PAGINATION
        mysqli_free_result($count_result);
        mysqli_stmt_close($stmt_proj_count);

        // Set total items for pagination FOR THIS VIEW
        $total_items_for_pagination = $total_municipalities;

        // Calculate pagination values based on $total_municipalities
        $total_pages = ceil($total_municipalities / $results_per_page);
        // Adjust current page if requested page is invalid for the *actual* total pages
        if ($current_page > $total_pages && $total_pages > 0) $current_page = $total_pages;
        if ($current_page < 1) $current_page = 1;
        $offset = ($current_page - 1) * $results_per_page; // <<<< THIS IS THE OFFSET FOR PROJECT VIEW

        // Get paginated data from tblproject (with search applied) using the calculated offset/limit
        $project_data_sql = "SELECT * FROM tblproject $project_where_clause ORDER BY municity LIMIT ? OFFSET ?";
        $stmt_proj_data = mysqli_prepare($conn, $project_data_sql);
        if (!$stmt_proj_data) throw new Exception("Error preparing project data query: " . mysqli_error($conn));

        $bind_params_proj_data = [];
        $bind_types_proj_data = "";
        if (!empty($project_search_params)) {
             $bind_params_proj_data = array_merge($project_search_params, [$results_per_page, $offset]);
             $bind_types_proj_data = $project_search_types . "ii";
        } else {
             $bind_params_proj_data = [$results_per_page, $offset];
             $bind_types_proj_data = "ii";
        }
        mysqli_stmt_bind_param($stmt_proj_data, $bind_types_proj_data, ...$bind_params_proj_data);

        if (!mysqli_stmt_execute($stmt_proj_data)) throw new Exception("Error executing project data query: " . mysqli_stmt_error($stmt_proj_data));
        $project_data_result = mysqli_stmt_get_result($stmt_proj_data);
        if (!$project_data_result) throw new Exception("Error fetching project data: " . mysqli_error($conn));
        mysqli_stmt_close($stmt_proj_data);

        // Initialize project counts array with data from tblproject for the current page
        $municipalities_on_page = [];
        while ($row = mysqli_fetch_assoc($project_data_result)) {
            $municipality = $row['municity'];
            $municipalities_on_page[] = $municipality;
            $project_counts[$municipality] = ['municipality' => $municipality, 'total' => 0];
            // Initialize all project counts to 0
            foreach ($projects as $project) {
                $project_counts[$municipality][$project] = 0;
            }
        }
        mysqli_free_result($project_data_result);

        // Mapping (same as before)
        $project_mapping = [ 'CYBERSECURITY' => 'cyber', 'ELGU' => 'elgu', 'FREEWIFI4ALL' => 'fwfa', 'IIDB' => 'iidb', 'ILCDB' => 'ilcdb', 'GECS' => 'gecs', 'DREAM' => 'dream', 'GOVNET' => 'govnet' ];

        // Only get activity counts for municipalities in our current page
        if (!empty($municipalities_on_page)) {
            $placeholders = implode(',', array_fill(0, count($municipalities_on_page), '?'));
            $activity_counts_sql = "SELECT municipality, project, COUNT(*) as count FROM tblactivity
                                   WHERE municipality IN ($placeholders)
                                   AND project IS NOT NULL AND project != ''
                                   GROUP BY municipality, project";
            $stmt_act_count = mysqli_prepare($conn, $activity_counts_sql);
            if (!$stmt_act_count) throw new Exception("Error preparing activity counts query: " . mysqli_error($conn));
            $types = str_repeat('s', count($municipalities_on_page));
            mysqli_stmt_bind_param($stmt_act_count, $types, ...$municipalities_on_page);
            if (!mysqli_stmt_execute($stmt_act_count)) throw new Exception("Error executing activity counts query: " . mysqli_stmt_error($stmt_act_count));
            $activity_counts_result = mysqli_stmt_get_result($stmt_act_count);
            if (!$activity_counts_result) throw new Exception("Error fetching activity counts: " . mysqli_error($conn));
            mysqli_stmt_close($stmt_act_count);

            while ($row = mysqli_fetch_assoc($activity_counts_result)) {
                $municipality = $row['municipality'];
                $project_name = strtoupper($row['project']);
                $count = (int)$row['count'];
                $project_column = isset($project_mapping[$project_name]) ? $project_mapping[$project_name] : strtolower($project_name);

                if (isset($project_counts[$municipality]) && in_array($project_column, $projects)) {
                    $project_counts[$municipality][$project_column] += $count;
                    $project_counts[$municipality]['total'] += $count;
                }
            }
            mysqli_free_result($activity_counts_result);
        }
        // Convert associative array to indexed array for easier iteration in the view
        $project_counts = array_values($project_counts);

    } catch (Exception $e) {
        $project_counts_error = "Error loading project activities: " . $e->getMessage();
        error_log("Project Activities View Error: " . $e->getMessage());
        // Reset pagination defaults if error occurred during calculation
        $total_pages = 1;
        $current_page = 1;
        $offset = 0;
        $total_items_for_pagination = 0;
    }
}

// --- END Project Activities View Data ---


// --- Date Calculations (For Stats) ---
$current_month_start = date('Y-m-01 00:00:00');
$current_month_end = date('Y-m-t 23:59:59');
$prev_month_timestamp = strtotime('-1 month', strtotime($current_month_start));
$prev_month_start = date('Y-m-01 00:00:00', $prev_month_timestamp);
$prev_month_end = date('Y-m-t 23:59:59', $prev_month_timestamp);

// --- Helper Function for Calculating Change ---
function calculateChange($current, $previous) {
    // ... (calculateChange function remains unchanged) ...
    $diff = $current - $previous;
    $percentage = 0;
    $direction = 'no_change';
    $icon_class = 'fa-minus';
    $color_class = 'text-neutral'; // CSS class for color

    if ($previous > 0) {
        $percentage = round(($diff / $previous) * 100);
    } elseif ($current > 0 && $previous == 0) {
        $percentage = 100; // Treat as 100% increase if starting from 0
        $direction = 'increase';
    }

    $formatted_diff = number_format(abs($diff));
    $current_month = date('F'); // Get current month name
    $prev_month_name = date('F', strtotime('-1 month')); // Get previous month name

    if ($diff > 0) {
        $direction = 'increase';
        $icon_class = 'fa-arrow-up';
        $color_class = 'text-green';
        $text = "{$formatted_diff} ({$percentage}%) more in {$current_month} vs {$prev_month_name}";
    } elseif ($diff < 0) {
        $direction = 'decrease';
        $icon_class = 'fa-arrow-down';
        $color_class = 'text-red';
        // Ensure percentage is shown correctly for decrease
        $text = "{$formatted_diff} (" . abs($percentage) . "%) less in {$current_month} vs {$prev_month_name}";
    } else {
         if ($current == 0 && $previous == 0) {
             $text = "No activity in {$current_month} or {$prev_month_name}";
         } else {
             $text = "No change in {$current_month} vs {$prev_month_name}";
         }
         // Keep neutral color/icon for no change
    }

    return [
        'difference' => $diff,
        'percentage' => $percentage, // Keep raw percentage for potential future use
        'direction' => $direction,
        'icon_class' => $icon_class,
        'color_class' => $color_class, // The crucial CSS class
        'text' => $text,
    ];
}

// --- Fetch Statistics Data (Conditional based on View) ---
$stats_data = []; $stats_data_current_month = []; $stats_data_previous = []; $stats = []; $stat_queries_ok = true;
if (!$db_connection_error) {
    // ... (fetchStatValue function definition remains unchanged) ...
    function fetchStatValue($conn, $sql, $params = [], $types = '') {
        $value = 0;
        $stmt = mysqli_prepare($conn, $sql);
        if ($stmt) {
            if (!empty($params) && !empty($types)) {
                 if (count($params) !== strlen($types)) {
                     error_log("Stat Fetch Mismatch Error: Params count (" . count($params) . ") != Types length (" . strlen($types) . ") | SQL: " . $sql);
                     mysqli_stmt_close($stmt);
                     $GLOBALS['stat_queries_ok'] = false;
                     return 0; // Return 0 on mismatch
                 }
                 mysqli_stmt_bind_param($stmt, $types, ...$params);
            }
            if (mysqli_stmt_execute($stmt)) {
                $result = mysqli_stmt_get_result($stmt);
                if ($result) {
                    $row = mysqli_fetch_assoc($result);
                    $value = $row['total'] ?? 0;
                    mysqli_free_result($result);
                } else {
                    error_log("Stat Fetch Result Error: " . mysqli_stmt_error($stmt) . " | SQL: " . $sql);
                    $GLOBALS['stat_queries_ok'] = false;
                }
            } else {
                error_log("Stat Execute Error: " . mysqli_stmt_error($stmt) . " | SQL: " . $sql);
                 $GLOBALS['stat_queries_ok'] = false;
            }
            mysqli_stmt_close($stmt);
        } else {
            error_log("Stat Prepare Error: " . mysqli_error($conn) . " | SQL: " . $sql);
             $GLOBALS['stat_queries_ok'] = false;
        }
        return is_numeric($value) ? (int)$value : 0;
    }

    // --- Define SQL Templates & Fetch Data (remains unchanged) ---
    $count_sql_monthly = "SELECT COUNT(*) AS total FROM tblactivity WHERE start >= ? AND start <= ?";
    $count_distinct_sql_monthly = "SELECT COUNT(DISTINCT %s) AS total FROM tblactivity WHERE %s IS NOT NULL AND %s != '' AND start >= ? AND start <= ?";
    $sum_sql_monthly = "SELECT SUM(%s) AS total FROM tblactivity WHERE start >= ? AND start <= ?";
    $count_district_sql_monthly = "SELECT COUNT(*) AS total FROM tblactivity WHERE district = ? AND start >= ? AND start <= ?";
    $types_ss = 'ss'; $types_sss = 'sss';
    $count_sql_all = "SELECT COUNT(*) AS total FROM tblactivity";
    $count_distinct_sql_all = "SELECT COUNT(DISTINCT %s) AS total FROM tblactivity WHERE %s IS NOT NULL AND %s != ''";
    $sum_sql_all = "SELECT SUM(%s) AS total FROM tblactivity";
    $count_district_sql_all = "SELECT COUNT(*) AS total FROM tblactivity WHERE district = ?";
    $types_s = 's';
    $district1_name = 'District 1 (Siargao Island)'; $district2_name = 'District 2 (Mainland)';

    // Fetch All-Time Data
    $stats_data['activities'] = fetchStatValue($conn, $count_sql_all);
    $stats_data['participants'] = fetchStatValue($conn, sprintf($sum_sql_all, 'participants'));
    $stats_data['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'sector', 'sector', 'sector'));
    $stats_data['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'agency', 'agency', 'agency'));
    $stats_data['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'municipality', 'municipality', 'municipality'));
    $stats_data['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'barangay', 'barangay', 'barangay'));
    $stats_data['district1'] = fetchStatValue($conn, $count_district_sql_all, [$district1_name], $types_s);
    $stats_data['district2'] = fetchStatValue($conn, $count_district_sql_all, [$district2_name], $types_s);

    // Fetch Current Month Data
    $date_params_current = [$current_month_start, $current_month_end];
    $stats_data_current_month['activities'] = fetchStatValue($conn, $count_sql_monthly, $date_params_current, $types_ss);
    $stats_data_current_month['participants'] = fetchStatValue($conn, sprintf($sum_sql_monthly, 'participants'), $date_params_current, $types_ss);
    $stats_data_current_month['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'sector', 'sector', 'sector'), $date_params_current, $types_ss);
    $stats_data_current_month['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'agency', 'agency', 'agency'), $date_params_current, $types_ss);
    $stats_data_current_month['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'municipality', 'municipality', 'municipality'), $date_params_current, $types_ss);
    $stats_data_current_month['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'barangay', 'barangay', 'barangay'), $date_params_current, $types_ss);
    $stats_data_current_month['district1'] = fetchStatValue($conn, $count_district_sql_monthly, [$district1_name, ...$date_params_current], $types_sss);
    $stats_data_current_month['district2'] = fetchStatValue($conn, $count_district_sql_monthly, [$district2_name, ...$date_params_current], $types_sss);

    // Fetch Previous Month Data
    $date_params_previous = [$prev_month_start, $prev_month_end];
    $stats_data_previous['activities'] = fetchStatValue($conn, $count_sql_monthly, $date_params_previous, $types_ss);
    $stats_data_previous['participants'] = fetchStatValue($conn, sprintf($sum_sql_monthly, 'participants'), $date_params_previous, $types_ss);
    $stats_data_previous['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'sector', 'sector', 'sector'), $date_params_previous, $types_ss);
    $stats_data_previous['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'agency', 'agency', 'agency'), $date_params_previous, $types_ss);
    $stats_data_previous['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'municipality', 'municipality', 'municipality'), $date_params_previous, $types_ss);
    $stats_data_previous['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'barangay', 'barangay', 'barangay'), $date_params_previous, $types_ss);
    $stats_data_previous['district1'] = fetchStatValue($conn, $count_district_sql_monthly, [$district1_name, ...$date_params_previous], $types_sss);
    $stats_data_previous['district2'] = fetchStatValue($conn, $count_district_sql_monthly, [$district2_name, ...$date_params_previous], $types_sss);
}

// --- Prepare the final $stats array for display (remains unchanged) ---
if ($stat_queries_ok) {
    // Build stats array
    $change_activities = calculateChange($stats_data_current_month['activities'], $stats_data_previous['activities']);
    $stats[] = [ 'key' => 'activities', 'icon' => 'fas fa-tasks', 'title' => 'Total Activities', 'value' => number_format($stats_data['activities']), 'insight' => $change_activities ];
    $change_participants = calculateChange($stats_data_current_month['participants'], $stats_data_previous['participants']);
    $stats[] = [ 'key' => 'participants', 'icon' => 'fas fa-users', 'title' => 'Total Participants', 'value' => number_format($stats_data['participants']), 'insight' => $change_participants ];
    $change_sectors = calculateChange($stats_data_current_month['sectors'], $stats_data_previous['sectors']);
    $stats[] = [ 'key' => 'sectors', 'icon' => 'fas fa-tags', 'title' => 'Total Unique Sectors', 'value' => number_format($stats_data['sectors']), 'insight' => $change_sectors ];
    $change_agencies = calculateChange($stats_data_current_month['agencies'], $stats_data_previous['agencies']);
    $stats[] = [ 'key' => 'agencies', 'icon' => 'fas fa-building', 'title' => 'Total Unique Agencies', 'value' => number_format($stats_data['agencies']), 'insight' => $change_agencies ];
    $change_municipalities = calculateChange($stats_data_current_month['municipalities'], $stats_data_previous['municipalities']);
    $stats[] = [ 'key' => 'municipalities', 'icon' => 'fas fa-map-marker-alt', 'title' => 'Total Unique Municipalities', 'value' => number_format($stats_data['municipalities']), 'insight' => $change_municipalities ];
    $change_barangays = calculateChange($stats_data_current_month['barangays'], $stats_data_previous['barangays']);
    $stats[] = [ 'key' => 'barangays', 'icon' => 'fas fa-map-pin', 'title' => 'Total Unique Barangays', 'value' => number_format($stats_data['barangays']), 'insight' => $change_barangays ];
    $change_district1 = calculateChange($stats_data_current_month['district1'], $stats_data_previous['district1']);
    $stats[] = [ 'key' => 'district1', 'icon' => 'fas fa-map', 'title' => 'Total District 1 Activities', 'value' => number_format($stats_data['district1']), 'insight' => $change_district1 ];
    $change_district2 = calculateChange($stats_data_current_month['district2'], $stats_data_previous['district2']);
    $stats[] = [ 'key' => 'district2', 'icon' => 'fas fa-map-signs', 'title' => 'Total District 2 Activities', 'value' => number_format($stats_data['district2']), 'insight' => $change_district2 ];
} else {
     $stats[] = ['icon' => 'fas fa-exclamation-circle', 'title' => 'Stats Error', 'value' => 'N/A', 'insight' => ['text' => 'Could not load statistics.', 'icon_class' => 'fa-exclamation-triangle', 'color_class' => 'text-red']];
}


// --- All Activities View Data (Fetch only if view = 'all') ---
$activities_result = false;
$activities_fetch_error = null;
$total_activities = 0; // Specific count for all activities view

if ($activities_view === 'all' && !$db_connection_error) {

    // --- Build WHERE clause and parameters for Activity Table (Search & Filters) ---
    $where_clauses = [];
    $query_params = [];
    $query_param_types = '';

    // 1. Add Search Condition (Searches across multiple columns)
    if (!empty($search_term)) {
        $like_term = '%' . $search_term . '%';
        $searchable_columns = [ 'project', 'subproject', 'activity', 'indicator', 'training', 'municipality', 'district', 'barangay', 'agency', 'mode', 'sector', 'person', 'resource', 'remarks', 'mov' ];
        $search_conditions = [];
        foreach ($searchable_columns as $column) {
            $search_conditions[] = "`" . $column . "` LIKE ?";
            $query_params[] = $like_term;
            $query_param_types .= 's';
        }
        $where_clauses[] = "(" . implode(" OR ", $search_conditions) . ")";
    }

    // 2. Add Filter Conditions
    foreach ($filter_columns as $column) {
        $filter_value = isset($_GET[$column]) ? trim($_GET[$column]) : '';
        if (!empty($filter_value)) {
            $where_clauses[] = "`$column` = ?";
            $query_params[] = $filter_value;
            $query_param_types .= 's';
        }
    }

    // 3. Construct Final WHERE Clause
    $sql_where_clause = '';
    if (!empty($where_clauses)) {
        $sql_where_clause = " WHERE " . implode(" AND ", $where_clauses);
    }

    // --- Calculate Total Filtered Activities ---
    $stmt_total = null;
    $total_activities_query = "SELECT COUNT(*) as total FROM tblactivity" . $sql_where_clause;
    $stmt_total = mysqli_prepare($conn, $total_activities_query);
    if ($stmt_total) {
        $total_result = null;
        if (!empty($query_params)) {
            if (count($query_params) !== strlen($query_param_types)) {
                error_log("Total Count Mismatch Error: Params (" . count($query_params) . ") != Types (" . strlen($query_param_types) . ")");
                $activities_fetch_error = "Error setting up activity count filter.";
            } else {
                 mysqli_stmt_bind_param($stmt_total, $query_param_types, ...$query_params);
                 if (!mysqli_stmt_execute($stmt_total)) { $activities_fetch_error = "Error counting filtered activities: " . mysqli_stmt_error($stmt_total); }
                 else { $total_result = mysqli_stmt_get_result($stmt_total); }
            }
        } else {
             if (!mysqli_stmt_execute($stmt_total)) { $activities_fetch_error = "Error counting total activities: " . mysqli_stmt_error($stmt_total); }
             else { $total_result = mysqli_stmt_get_result($stmt_total); }
        }

        if ($activities_fetch_error === null && $total_result) {
             $total_row = mysqli_fetch_assoc($total_result);
             $total_activities = $total_row['total'] ?? 0; // <<<< THIS IS THE TOTAL FOR ALL ACTIVITIES VIEW
             mysqli_free_result($total_result);
        } elseif ($activities_fetch_error === null) {
             error_log("Total Filtered Activities Count Result Error: " . mysqli_stmt_error($stmt_total));
             $activities_fetch_error = "Error reading activity count.";
        }
        mysqli_stmt_close($stmt_total);

    } else {
        error_log("Total Filtered Activities Count Prepare Error: " . mysqli_error($conn));
        $activities_fetch_error = "Error preparing activity count.";
    }

    // Set total items for pagination FOR THIS VIEW
    $total_items_for_pagination = $total_activities;

    // --- Fetch Paginated Activities ---
    if ($activities_fetch_error === null) { // Proceed even if count is 0 to show message
        // Calculate pagination values based on $total_activities
        $total_pages = ceil($total_activities / $results_per_page);
        // Adjust current page if requested page is invalid
        if ($current_page > $total_pages && $total_pages > 0) $current_page = $total_pages;
        if ($current_page < 1) $current_page = 1;
        $offset = ($current_page - 1) * $results_per_page; // <<<< THIS IS THE OFFSET FOR ALL ACTIVITIES VIEW

        if ($total_activities > 0) {
            $stmt_activities = null;
            $activities_sql = "SELECT id, start, end, project, subproject, activity, indicator, training, municipality, district, barangay, agency, mode, sector, person, resource, participants, completers, male, female, approved, mov, remarks
                               FROM tblactivity" . $sql_where_clause .
                               " ORDER BY start DESC, id DESC LIMIT ? OFFSET ?";

            $all_bind_params = $query_params;
            $all_bind_params[] = $results_per_page;
            $all_bind_params[] = $offset;
            $combined_param_types = $query_param_types . 'ii';

            $stmt_activities = mysqli_prepare($conn, $activities_sql);
            if ($stmt_activities) {
                 if (count($all_bind_params) !== strlen($combined_param_types)) {
                      error_log("Activity Fetch Mismatch Error: Params (" . count($all_bind_params) . ") != Types (" . strlen($combined_param_types) . ")");
                      $activities_fetch_error = "Error setting up activity data filter.";
                 } else {
                     mysqli_stmt_bind_param($stmt_activities, $combined_param_types, ...$all_bind_params);
                     if (!mysqli_stmt_execute($stmt_activities)) { $activities_fetch_error = "Error fetching activity data: " . mysqli_stmt_error($stmt_activities); }
                     else { $activities_result = mysqli_stmt_get_result($stmt_activities); } // Assign result object
                 }
                 // Don't close statement here if using result later
            } else {
                $activities_fetch_error = "Error preparing to fetch activities: " . mysqli_error($conn);
            }
        } else {
             // No activities found based on count
             $is_filtered_all = !empty($search_term) || !empty(array_filter(array_intersect_key($_GET, array_flip($filter_columns))));
             $activities_fetch_error = $is_filtered_all ? "No activities found matching the criteria." : "No activities found in the database.";
             $total_pages = 1; // Ensure at least page 1 exists conceptually
        }
    } // end if no count error

} elseif ($activities_view === 'all' && $db_connection_error) {
    $activities_fetch_error = "Cannot fetch activities due to database connection error.";
    $total_pages = 1;
    $total_activities = 0;
    $total_items_for_pagination = 0;
}


// --- Build URL Parameters string for pagination links ---
// (Moved down to ensure $activities_view is definitively set)
$url_params_array = [];
if (!empty($search_term)) $url_params_array['search'] = $search_term;
if ($results_per_page !== $default_results_per_page) $url_params_array['limit'] = $results_per_page;
// Include other active filters
foreach ($filter_columns as $column) {
    if (isset($_GET[$column]) && trim($_GET[$column]) !== '') {
        $url_params_array[$column] = trim($_GET[$column]);
    }
}
// Always include the current view in pagination links
$url_params_array['view'] = $activities_view;

// Build query string *without* the 'page' parameter
$url_params_for_pagination = !empty($url_params_array) ? '&' . http_build_query($url_params_array) : '';


// --- Helper Functions ---
function formatDateForDisplay($date_string) { /* ... unchanged ... */ if (empty($date_string) || $date_string === '0000-00-00' || $date_string === '0000-00-00 00:00:00') return ''; $timestamp = strtotime($date_string); return $timestamp ? date('M d, Y', $timestamp) : ''; }
function formatDateForInput($date_string) { /* ... unchanged ... */ if (empty($date_string) || $date_string === '0000-00-00' || $date_string === '0000-00-00 00:00:00') return ''; $timestamp = strtotime($date_string); return $timestamp ? date('Y-m-d', $timestamp) : ''; }

// --- Table Colspan ---
$table_colspan = 23; // For 'all' activities view

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Activity Monitoring</title>
    <link rel="stylesheet" href="css/style.css"> <!-- Ensure this path is correct -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* ... existing styles ... */
        th { font-weight: bold !important; }
        .participants-col { text-align: center; }
        .db-error-message { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 15px; margin: 15px; border-radius: 4px; }
        .table-message.error { color: var(--red-color); background-color: rgba(255, 0, 0, 0.05); }
        .table-message { text-align: center; padding: 20px; color: var(--text-light); }
        .inactive-row td { opacity: 0.6; font-style: italic; }
        .btn-view-switch { /* ... styles from your CSS ... */ }
        .btn-view-switch.active { /* ... styles from your CSS ... */ }
        /* Ensure pagination styles are present */
        .pagination-controls { display: flex; justify-content: space-between; align-items: center; padding: 15px 0; flex-wrap: wrap; gap: 10px; }
        .pagination-info { font-size: 0.9em; color: var(--text-light); }
        .pagination-nav { display: flex; gap: 5px; }
        .pagination-nav .btn { /* ... styles from your CSS ... */ }
        .pagination-nav .btn.disabled { /* ... styles from your CSS ... */ }
        .pagination-nav .btn.active { /* ... styles from your CSS ... */ }

        /* Search field styling */
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
            flex: 1;
            min-width: 0;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: all 0.2s ease;
            background-color: var(--bg-light);
            width: 100%;
            height: 38px;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.1);
        }

        .search-form {
            width: 100%;
            max-width: 300px;
        }

        .table-right-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Modern search field with icon */
        .search-container {
            position: relative;
            width: 100%;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 14px;
            z-index: 2;
            pointer-events: none;
        }

        .search-input {
            padding-left: 35px !important;
            padding-right: 10px !important;
            transition: all 0.3s ease;
            width: 100%;
        }

        /* Welcome Modal Styles */
        .welcome-section {
            padding: 0 10px;
        }

        .welcome-section h3 {
            margin-top: 25px;
            margin-bottom: 10px;
            color: var(--primary-color);
            font-size: 18px;
            font-weight: 600;
            border-bottom: 1px solid var(--border-light);
            padding-bottom: 8px;
        }

        .welcome-section h3 i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .welcome-section p {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .welcome-section ul {
            margin-left: 25px;
            margin-bottom: 20px;
        }

        .welcome-section li {
            margin-bottom: 8px;
            line-height: 1.5;
            position: relative;
        }

        .welcome-section li::before {
            content: "•";
            color: var(--primary-color);
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
        }

        /* Get Started Button Style */
        #welcomeModal .modal-footer {
            display: flex;
            justify-content: flex-end;
            padding-top: 20px;
        }

        #welcomeModal .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: background-color 0.2s ease;
        }

        #welcomeModal .btn-primary:hover {
            background-color: #5a4bd3;
        }

        /* Additional styles for this page */

        /* Statistics Card Styles */
        .stat-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
        }

        /* Statistics Breakdown Modal Styles (matching ilcdb.php exactly) */
        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0;
        }

        .stats-tabs {
            display: flex;
        }

        .stats-tab-btn {
            padding: 8px 15px;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            color: var(--text-light);
            transition: all 0.2s ease;
        }

        .stats-tab-btn:hover {
            color: var(--primary-color);
        }

        .stats-tab-btn.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }

        .stats-view {
            margin-top: 15px;
        }

        /* Statistics Table Controls and Pagination Styles */
        .stats-table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 8px 12px;
        }

        .stats-table-length {
            display: flex;
            align-items: center;
            white-space: nowrap;
            font-size: 13px;
            color: #666;
        }

        .stats-table-length select {
            margin: 0 5px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            width: 50px;
            font-size: 13px;
            color: #333;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 6px center;
            background-size: 12px;
            padding-right: 20px;
        }

        .stats-table-search {
            display: flex;
            align-items: center;
            white-space: nowrap;
            font-size: 13px;
            color: #666;
            position: relative;
        }

        .stats-table-search input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            color: #333;
            background-color: white;
            width: 250px;
        }

        .stats-table-container {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 15px;
            border-top: 1px solid var(--border-light);
            border-bottom: 1px solid var(--border-light);
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
        }

        .stats-table th,
        .stats-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-light);
        }

        .stats-table th {
            background-color: var(--bg-light);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .stats-table tr:hover {
            background-color: rgba(106, 90, 224, 0.05);
        }

        .stats-table-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding: 8px 12px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .stats-table-info {
            color: #666;
            font-size: 13px;
        }

        .stats-table-pages {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background-color: #fff;
            border-radius: 4px;
            cursor: pointer;
            color: #333;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not([disabled]) {
            background-color: #f0f0f0;
            color: #333;
        }

        .pagination-btn[disabled] {
            opacity: 0.5;
            cursor: not-allowed;
            color: #999;
        }

        .pagination-numbers {
            display: flex;
            margin: 0 5px;
        }

        .page-number {
            padding: 6px 10px;
            border: 1px solid #ddd;
            background-color: #fff;
            color: #333;
            text-decoration: none;
            font-size: 13px;
            transition: all 0.2s ease;
            margin: 0 1px;
            border-radius: 4px;
        }

        .page-number:hover {
            background-color: #f0f0f0;
            color: #333;
        }

        .page-number.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Participants View Toggle Styles */
        .stats-view-toggle {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: var(--bg-light);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-light);
        }

        .toggle-label {
            font-weight: 500;
            margin-right: 15px;
            color: var(--text-color);
        }

        .toggle-buttons {
            display: flex;
            background-color: var(--bg-light);
            border-radius: 20px;
            padding: 3px;
            border: 1px solid var(--border-color);
        }

        .toggle-btn {
            padding: 6px 15px;
            border: none;
            background: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 500;
            color: var(--text-light);
            transition: all 0.2s ease;
        }

        .toggle-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* Chart Summary and Chart Type Selector Styles (matching ilcdb.php exactly) */
        .chart-summary {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 15px;
            padding: 15px;
            background-color: var(--bg-light);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-light);
            flex-wrap: wrap;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-right: 60px;
        }

        .summary-label {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .chart-type-selector {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }

        .chart-type-btn {
            margin-left: 0;
        }

        .chart-type-btn:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }

        .chart-type-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .chart-container {
            height: 350px;
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius);
            padding: 15px;
            background-color: white;
        }

        .blank-chart-message {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 250px;
            margin-bottom: 15px;
            padding: 20px;
            background-color: var(--bg-light);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-light);
            color: var(--text-light);
            font-size: 16px;
            text-align: center;
        }

        /* Loading and Error Indicators */
        .loading-indicator,
        .error-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            text-align: center;
            color: var(--text-light);
        }

        .loading-indicator i,
        .error-indicator i {
            margin-right: 10px;
            font-size: 18px;
        }

        .error-indicator {
            color: var(--red-color);
            background-color: rgba(255, 0, 0, 0.05);
            border-radius: var(--border-radius);
        }

        .loading-indicator.active i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Custom Notification Element -->
    <div id="notification" class="notification"> <p class="notification-message" id="notificationMessage"></p> </div>

    <div class="app-container">
        <?php
        // Set current page name for sidebar highlighting
        $current_page_name = 'index';

        // Include the sidebar
        include 'sidebar.php';
        ?>

        <!-- ========== App Main Content ========== -->
        <main class="app-main">
            <!-- *** Dashboard Header *** -->
            <div class="dashboard-header">
                <!-- ... Header content remains unchanged ... -->
                <div class="office-header">
                    <div class="office-logo"> <img src="images/dict-logo.png" alt="DICT Logo"> </div>
                    <div class="office-info"> <h1>DICT Surigao del Norte Provincial Office</h1> <p>Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p> </div>
                </div>
            </div>
            <!-- *** END Dashboard Header *** -->

             <?php if ($db_connection_error): ?>
                 <div class="db-error-message"> <strong>Database Error:</strong> <?php echo htmlspecialchars($db_connection_error); ?> </div>
             <?php endif; ?>

             <?php if (isset($_SESSION['error_message'])): ?>
                 <div class="db-error-message"> <strong>Error:</strong> <?php echo htmlspecialchars($_SESSION['error_message']); ?> </div>
                 <?php unset($_SESSION['error_message']); // Clear the message after displaying it ?>
             <?php endif; ?>

            <!-- Stats Cards Section -->
            <section class="stats-cards">
                 <!-- ... Stats card rendering remains unchanged ... -->
                 <?php if (!empty($stats)): ?>
                    <?php foreach ($stats as $stat): ?>
                    <?php $has_insight = isset($stat['insight']) && is_array($stat['insight']); ?>
                    <div class="stat-card" data-key="<?php echo htmlspecialchars($stat['key'] ?? ''); ?>">
                        <div class="card-header">
                            <h4><?php echo htmlspecialchars($stat['title'] ?? 'N/A'); ?></h4>
                            <?php $icon_class = str_replace('fas fa-', '', $stat['icon'] ?? 'fa-chart-bar'); ?>
                            <div class="stat-card-icon icon-<?php echo htmlspecialchars($icon_class); ?>">
                                <i class="<?php echo htmlspecialchars($stat['icon'] ?? 'fas fa-chart-bar'); ?>"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="value"><?php echo htmlspecialchars($stat['value'] ?? 'N/A'); ?></p>
                            <?php if ($has_insight): ?>
                                <?php $insight_color_class = htmlspecialchars($stat['insight']['color_class'] ?? 'text-neutral'); ?>
                                <div class="stat-insight <?php echo $insight_color_class; ?>">
                                    <i class="fas <?php echo htmlspecialchars($stat['insight']['icon_class'] ?? 'fa-minus'); ?>"></i>
                                    <span><?php echo htmlspecialchars($stat['insight']['text'] ?? 'No comparison data.'); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                 <?php else: ?>
                     <?php if (!$db_connection_error && empty($stats)): ?> <p>Loading statistics...</p> <?php endif; ?>
                     <?php if ($db_connection_error || !$stat_queries_ok): ?>
                         <div class="stat-card">
                            <div class="card-header"> <h4>Status</h4> <div class="stat-card-icon icon-exclamation-circle"> <i class="fas fa-exclamation-circle"></i> </div> </div>
                            <div class="card-body"> <p class="value" style="font-size: 18px; color: var(--red-color);">Error</p> <div class="stat-insight text-red"> <i class="fas fa-exclamation-triangle"></i> <span>Cannot load statistics.</span> </div> </div>
                         </div>
                     <?php endif; ?>
                <?php endif; ?>
            </section>


            <!-- Activities Table Section -->
            <section class="recent-activities card">
                 <div class="activities-header">
                    <h2>
                        <?php echo ($activities_view === 'project') ? 'Project Activities (by Municipality)' : 'All Activities'; ?>
                    </h2>
                    <!-- View Switch Button -->
                    <div class="view-switch-container">
                        <?php
                        // Build base URLs for view switching, preserving other params EXCEPT page
                        $switch_params_all = $url_params_array; unset($switch_params_all['page']); $switch_params_all['view'] = 'all';
                        $switch_params_proj = $url_params_array; unset($switch_params_proj['page']); $switch_params_proj['view'] = 'project';
                        $all_view_url = 'index.php?' . http_build_query($switch_params_all);
                        $project_view_url = 'index.php?' . http_build_query($switch_params_proj);
                        ?>
                        <a href="<?php echo $all_view_url; ?>" class="btn-view-switch <?php echo $activities_view === 'all' ? 'active' : ''; ?>">
                            All Activities
                        </a>
                        <a href="<?php echo $project_view_url; ?>" class="btn-view-switch <?php echo $activities_view === 'project' ? 'active' : ''; ?>">
                            Project Activities
                        </a>
                    </div>
                 </div>

                 <div class="table-controls">
                     <!-- Left control: Results per page form -->
                    <form action="index.php" method="get" id="resultsPerPageForm" class="results-per-page-form">
                        <!-- Pass current view -->
                        <input type="hidden" name="view" value="<?php echo htmlspecialchars($activities_view); ?>">
                        <!-- Pass search term -->
                        <?php if (!empty($search_term)): ?> <input type="hidden" name="search" value="<?php echo htmlspecialchars($search_term); ?>"> <?php endif; ?>
                        <!-- Pass filters -->
                        <?php foreach ($filter_columns as $column): ?>
                            <?php if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?>
                                <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>">
                            <?php endif; ?>
                        <?php endforeach; ?>
                        <!-- Reset to page 1 on limit change -->
                        <input type="hidden" name="page" value="1">
                        <label for="resultsPerPageSelect">Results per page:</label>
                        <select name="limit" id="resultsPerPageSelect" onchange="this.form.submit();">
                            <?php foreach($results_per_page_options as $option): ?>
                            <option value="<?php echo $option; ?>" <?php if($option == $results_per_page) echo 'selected'; ?>><?php echo $option; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </form>
                     <!-- Right control: Search form -->
                    <div class="table-right-controls">
                        <form action="index.php" method="get" class="search-form">
                            <div class="filter-group search-container">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="search" name="search" class="form-control search-input"
                                       placeholder="Search in actions, details..."
                                       value="<?php echo htmlspecialchars($search_term); ?>">
                                <!-- Pass current view -->
                                <input type="hidden" name="view" value="<?php echo htmlspecialchars($activities_view); ?>">
                                <!-- Pass current limit -->
                                <?php if ($results_per_page !== $default_results_per_page): ?> <input type="hidden" name="limit" value="<?php echo $results_per_page; ?>"> <?php endif; ?>
                                <!-- Pass filters -->
                                <?php foreach ($filter_columns as $column): ?>
                                    <?php if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?>
                                        <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>">
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </form>
                        <!-- Filter controls removed as requested -->
                    </div>
                 </div>

                 <!-- Filter Dropdown Area removed as requested -->
                 <div class="table-container">
                     <?php if ($activities_view === 'project'): ?>
                     <!-- ====================== -->
                     <!-- Project Activities View -->
                     <!-- ====================== -->
                     <table>
                         <thead>
                             <tr>
                                 <th class="col-rownum">#</th>
                                 <th>Municipality</th>
                                 <?php if (isset($projects) && is_array($projects)): ?>
                                     <?php
                                     $project_display_names = [ 'cyber' => 'CYBERSECURITY', 'elgu' => 'ELGU', 'fwfa' => 'FREEWIFI4ALL', 'iidb' => 'IIDB', 'ilcdb' => 'ILCDB', 'gecs' => 'GECS', 'dream' => 'DREAM', 'govnet' => 'GOVNET' ];
                                     foreach ($projects as $project):
                                         $display_name = isset($project_display_names[$project]) ? $project_display_names[$project] : strtoupper($project);
                                     ?>
                                         <th class="participants-col"><?php echo htmlspecialchars($display_name); ?></th>
                                     <?php endforeach; ?>
                                 <?php endif; ?>
                                 <th class="participants-col">Total</th>
                             </tr>
                         </thead>
                         <tbody>
                             <?php if ($project_counts_error): ?>
                                 <tr>
                                     <td colspan="<?php echo isset($projects) ? count($projects) + 3 : 3; ?>" class="table-message error">
                                         <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($project_counts_error); ?>
                                     </td>
                                 </tr>
                             <?php elseif (!empty($project_counts)): ?>
                                 <?php foreach ($project_counts as $row_index => $row): ?>
                                     <?php $row_number = $offset + $row_index + 1; // Use offset calculated for project view ?>
                                     <tr <?php echo ($row['total'] > 0) ? '' : 'class="inactive-row"'; ?>>
                                         <td class="col-rownum"><?php echo $row_number; ?></td>
                                         <td><?php echo htmlspecialchars($row['municipality'] ?? ''); ?></td>
                                         <?php if (isset($projects) && is_array($projects)): ?>
                                             <?php foreach ($projects as $project): ?>
                                                 <td class="participants-col"><?php echo number_format((int)($row[$project] ?? 0)); ?></td>
                                             <?php endforeach; ?>
                                         <?php endif; ?>
                                         <td class="participants-col"><strong><?php echo number_format((int)($row['total'] ?? 0)); ?></strong></td>
                                     </tr>
                                 <?php endforeach; ?>
                             <?php else: // No error, but no municipalities found (could be due to search) ?>
                                 <tr>
                                     <td colspan="<?php echo isset($projects) ? count($projects) + 3 : 3; ?>" class="table-message">
                                         <i class="fas fa-info-circle"></i>
                                         <?php echo !empty($search_term) ? 'No municipalities found matching "' . htmlspecialchars($search_term) . '".' : 'No project activity data found.'; ?>
                                     </td>
                                 </tr>
                             <?php endif; ?>
                         </tbody>
                     </table>
                     <?php else: ?>
                     <!-- ====================== -->
                     <!-- All Activities View    -->
                     <!-- ====================== -->
                     <table>
                         <thead>
                             <tr>
                                 <th class="col-rownum">#</th>
                                 <th>Start Date</th><th>End Date</th><th>Project</th><th>Subproject</th><th>Activity</th><th>Indicator</th><th>Training?</th><th>Municipality</th><th>District</th><th>Barangay</th><th>Agency</th><th>Mode</th><th>Sector</th><th>Person(s)</th><th>Resource Person(s)</th><th>Participants</th><th>Completers</th><th>Male</th><th>Female</th><th>Approved?</th><th>MOV</th><th>Remarks</th>
                             </tr>
                         </thead>
                         <tbody>
                             <?php if ($activities_fetch_error && !$activities_result): // Display error or 'no results' message ?>
                                 <tr>
                                     <td colspan="<?php echo $table_colspan; ?>" class="table-message <?php echo str_contains(strtolower($activities_fetch_error), 'error') ? 'error' : ''; ?>">
                                        <?php if (str_contains(strtolower($activities_fetch_error), 'error')): ?><i class="fas fa-exclamation-triangle"></i>
                                        <?php elseif (!empty($search_term) || !empty(array_filter(array_intersect_key($_GET, array_flip($filter_columns))))): ?><i class="fas fa-filter"></i>
                                        <?php else: ?><i class="fas fa-info-circle"></i><?php endif; ?>
                                        <?php echo htmlspecialchars($activities_fetch_error); ?>
                                     </td>
                                 </tr>
                             <?php elseif ($activities_result && mysqli_num_rows($activities_result) > 0): ?>
                                 <?php while ($activity = mysqli_fetch_assoc($activities_result)): ?>
                                     <?php $row_number = $offset + ($activity_row_index ?? 0) + 1; // Use offset calculated for all activities view ?>
                                     <tr data-activity-id="<?php echo htmlspecialchars($activity['id']); ?>">
                                         <td class="col-rownum"><?php echo $row_number; ?></td>
                                         <td class="date-col"><?php echo formatDateForDisplay($activity['start']); ?></td>
                                         <td class="date-col"><?php echo formatDateForDisplay($activity['end']); ?></td>
                                         <td><?php echo htmlspecialchars($activity['project'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['subproject'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['activity'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['indicator'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['training'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['municipality'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['district'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['barangay'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['agency'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['mode'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['sector'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['person'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['resource'] ?? ''); ?></td>
                                         <td class="participants-col"><?php echo number_format((int)($activity['participants'] ?? 0)); ?></td>
                                         <td class="participants-col"><?php echo number_format((int)($activity['completers'] ?? 0)); ?></td>
                                         <td class="participants-col"><?php echo number_format((int)($activity['male'] ?? 0)); ?></td>
                                         <td class="participants-col"><?php echo number_format((int)($activity['female'] ?? 0)); ?></td>
                                         <td><?php echo htmlspecialchars($activity['approved'] ?? ''); ?></td>
                                         <td><?php echo htmlspecialchars($activity['mov'] ?? ''); ?></td>
                                         <td><?php echo nl2br(htmlspecialchars($activity['remarks'] ?? '')); ?></td>
                                     </tr>
                                     <?php $activity_row_index = ($activity_row_index ?? 0) + 1; ?>
                                 <?php endwhile; ?>
                                 <?php mysqli_free_result($activities_result); // Free result set ?>
                             <?php else: // Should have been caught by $activities_fetch_error, but as a fallback ?>
                                 <tr> <td colspan="<?php echo $table_colspan; ?>" class="table-message"> <i class="fas fa-info-circle"></i> No activities found. </td> </tr>
                             <?php endif; ?>
                             <?php
                                if (isset($stmt_activities) && is_object($stmt_activities)) mysqli_stmt_close($stmt_activities); // Close statement if it was prepared
                             ?>
                         </tbody>
                     </table>
                     <?php endif; ?>
                 </div>
                 <!-- ====================== -->
                 <!-- Pagination Controls    -->
                 <!-- ====================== -->
                 <div class="pagination-controls">
                    <div class="pagination-info">
                        <?php
                        // Use the final calculated $total_items_for_pagination and $offset for the current view
                        if ($total_items_for_pagination > 0) {
                             $start_index = $offset + 1;
                             $end_index = 0;
                             $item_label = '';
                             if ($activities_view === 'project') {
                                 $item_label = 'municipalities';
                                 // $current_count = count($project_counts); // Count items actually displayed on the page
                                 // Workaround if count($project_counts) isn't reliable: use $results_per_page
                                 $end_index = min($offset + $results_per_page, $total_items_for_pagination);
                             } else { // all activities view
                                 $item_label = 'activities';
                                 // Workaround if mysqli_num_rows($activities_result) isn't available here: use $results_per_page
                                  $end_index = min($offset + $results_per_page, $total_items_for_pagination);
                             }
                             $filter_text = '';
                              if (!empty($search_term)) { $filter_text .= ' (filtered by "' . htmlspecialchars($search_term) . '")'; }
                              elseif ($activities_view === 'all' && !empty(array_filter(array_intersect_key($_GET, array_flip($filter_columns))))) { $filter_text .= ' (filtered)'; }

                             echo "Showing " . $start_index . " - " . $end_index . " of " . number_format($total_items_for_pagination) . " " . $item_label . $filter_text;

                         } else {
                             $item_label = ($activities_view === 'project') ? 'municipalities' : 'activities';
                             $filter_text = '';
                             if (!empty($search_term)) { $filter_text .= ' (filtered by "' . htmlspecialchars($search_term) . '")'; }
                             elseif ($activities_view === 'all' && !empty(array_filter(array_intersect_key($_GET, array_flip($filter_columns))))) { $filter_text .= ' (filtered)'; }
                             echo "Showing 0 - 0 of 0 " . $item_label . $filter_text;
                         }
                        ?>
                    </div>
                    <div class="pagination-nav">
                         <?php
                         // Use the final $current_page and $total_pages calculated for the current view
                         // Build the base link structure ONCE
                         $page_link_base = "index.php?page=";

                         // Previous Button
                         if ($current_page > 1): ?>
                             <a href="<?php echo $page_link_base . ($current_page - 1) . $url_params_for_pagination; ?>" class="btn btn-nav">Previous</a>
                         <?php else: ?>
                             <span class="btn btn-nav disabled">Previous</span>
                         <?php endif; ?>

                         <?php
                         // Page Number Links - Use the final $total_pages
                         if ($total_pages > 1) {
                             $max_links = 5;
                             $start_link = max(1, $current_page - floor($max_links / 2));
                             $end_link = min($total_pages, $start_link + $max_links - 1);
                             // Adjust start link if end link reaches the total pages limit
                             if ($end_link == $total_pages && $total_pages >= $max_links) {
                                $start_link = max(1, $total_pages - $max_links + 1);
                             }

                             // Ellipsis and first page link
                             if ($start_link > 1) {
                                 echo '<a href="'.$page_link_base.'1'.$url_params_for_pagination.'" class="btn btn-page">1</a>';
                                 if ($start_link > 2) {
                                     echo '<span class="btn btn-page disabled">...</span>';
                                 }
                             }

                             // Numbered page links
                             for ($i = $start_link; $i <= $end_link; $i++):
                                 echo '<a href="'.$page_link_base.$i.$url_params_for_pagination.'" class="btn btn-page '.($i == $current_page ? 'active' : '').'">'.$i.'</a>';
                             endfor;

                             // Ellipsis and last page link
                             if ($end_link < $total_pages) {
                                 if ($end_link < $total_pages - 1) {
                                     echo '<span class="btn btn-page disabled">...</span>';
                                 }
                                 echo '<a href="'.$page_link_base.$total_pages.$url_params_for_pagination.'" class="btn btn-page">'.$total_pages.'</a>';
                             }
                         } elseif ($total_items_for_pagination > 0 && $total_pages == 1) {
                             // Show page 1 if there's exactly one page of results
                             echo '<a href="'.$page_link_base.'1'.$url_params_for_pagination.'" class="btn btn-page active">1</a>';
                         }
                         // If total_items is 0, no page numbers will be shown, which is correct.
                         ?>

                         <?php // Next Button - Use the final $total_pages
                         if ($current_page < $total_pages): ?>
                             <a href="<?php echo $page_link_base . ($current_page + 1) . $url_params_for_pagination; ?>" class="btn btn-nav">Next</a>
                         <?php else: ?>
                             <span class="btn btn-nav disabled">Next</span>
                         <?php endif; ?>
                    </div>
                </div>
            </section>
        </main>
    </div> <!-- End .app-container -->

    <!-- Modals (remain unchanged structurally, only triggers removed) -->
    <!-- Add Modal --> <div id="addModal" class="modal"> <!-- ... modal content ... --> </div>
    <!-- Edit Modal --> <div id="editModal" class="modal"> <!-- ... modal content ... --> </div>
    <!-- Delete Modal --> <div id="deleteModal" class="modal"> <!-- ... modal content ... --> </div>
    <!-- Upload Modal --> <div id="uploadModal" class="modal"> <!-- ... modal content ... --> </div>
    <!-- View Files Modal --> <div id="viewFilesModal" class="modal"> <!-- ... modal content ... --> </div>

    <!-- Update Profile Modal -->
    <div id="updateProfileModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-user-edit"></i> Update Profile</h2>
                <button type="button" class="close-modal" data-modal-id="updateProfile">&times;</button>
            </div>
            <div class="modal-body">
                <div id="updateProfileLoadingIndicator" class="loading-indicator active">
                    <i class="fas fa-spinner fa-spin"></i> Loading profile data...
                </div>
                <div id="updateProfileErrorIndicator" class="error-indicator" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span>Could not load profile data.</span>
                </div>
                <form id="updateProfileForm" class="form-grid" style="display: none;">
                    <input type="hidden" id="profile_user_id" name="user_id">

                    <div class="form-field">
                        <label for="profile_username">Username <span class="required">*</span></label>
                        <input type="text" id="profile_username" name="username" required class="form-control">
                    </div>

                    <div class="form-field">
                        <label for="profile_full_name">Full Name <span class="required">*</span></label>
                        <input type="text" id="profile_full_name" name="full_name" required class="form-control">
                    </div>

                    <div class="form-field">
                        <label for="profile_email">Email</label>
                        <input type="email" id="profile_email" name="email" class="form-control">
                    </div>

                    <div class="password-section">
                        <h3>Change Password</h3>
                        <p><small>Leave blank to keep current password</small></p>

                        <div class="form-field">
                            <label for="profile_password">New Password</label>
                            <input type="password" id="profile_password" name="password" class="form-control">
                            <small>Minimum 8 characters</small>
                        </div>

                        <div class="form-field">
                            <label for="profile_confirm_password">Confirm New Password</label>
                            <input type="password" id="profile_confirm_password" name="confirm_password" class="form-control">
                        </div>
                    </div>

                    <div id="updateProfileError" class="error-message"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="updateProfile">Cancel</button>
                <button type="submit" form="updateProfileForm" id="saveProfileButton" class="btn btn-primary" disabled><i class="fas fa-save"></i> Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Logout Confirm Modal -->
    <div id="logoutConfirmModal" class="modal small">
         <div class="modal-content">
             <div class="modal-header">
                 <h2>Confirm Logout</h2>
                 <button type="button" class="close-modal" data-modal-id="logoutConfirm">&times;</button>
             </div>
             <div class="modal-body">
                 <div class="warning-icon-center"><i class="fas fa-exclamation-triangle warning-icon"></i></div>
                 <p>Are you sure you want to log out?</p>
                 <p class="warning-message">You will need to log in again to access the system.</p>
             </div>
             <div class="modal-footer">
                 <button type="button" class="btn btn-secondary close-modal" data-modal-id="logoutConfirm">Cancel</button>
                 <button type="button" id="confirmLogoutButton" class="btn btn-danger">Log Out</button>
             </div>
         </div>
    </div>

    <!-- Welcome Modal -->
    <div id="welcomeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-hands-helping"></i> Welcome to DICT SDN Provincial Office System</h2>
                <button type="button" class="close-modal" data-modal-id="welcome">&times;</button>
            </div>
            <div class="modal-body">
                <div class="welcome-section">
                    <div class="welcome-greeting">Hello, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</div>
                    <p>Welcome to the DICT Surigao del Norte Provincial Office Activity Monitoring System. You are logged in as <strong><?php echo htmlspecialchars($_SESSION['username']); ?></strong> with <strong><?php echo htmlspecialchars($_SESSION['role']); ?></strong> privileges.</p>

                    <h3><i class="fas fa-lock"></i> Your Access Level</h3>
                    <?php if ($_SESSION['role'] === 'admin'): ?>
                    <p>As an <strong>administrator</strong>, you have full access to all features of the system, including:</p>
                    <ul>
                        <li>View, add, edit, and delete activities across all bureaus</li>
                        <li>Manage user credentials</li>
                        <li>Access system logs</li>
                        <li>Upload and view files for all activities</li>
                        <li>Import and export data</li>
                    </ul>
                    <?php else: ?>
                    <p>Your access is based on your assigned bureau: <strong><?php echo htmlspecialchars($_SESSION['bureau'] ?? 'Not Assigned'); ?></strong></p>
                    <ul>
                        <li>You have <strong>full access</strong> to your assigned bureau's page</li>
                        <li>For other bureaus, you have <strong>limited access</strong> (view and export only)</li>
                        <li>You cannot edit, delete, upload files, or add new items to bureaus other than your own</li>
                    </ul>
                    <?php endif; ?>

                    <h3><i class="fas fa-info-circle"></i> System Features</h3>
                    <p>This system allows you to:</p>
                    <ul>
                        <li>Track and manage activities across different DICT programs</li>
                        <li>Monitor participants and completers for each activity</li>
                        <li>Generate statistics and reports</li>
                        <li>Manage inventory items</li>
                        <li>Track Tech4ED DTCs and FreeWifi4All letter requests</li>
                    </ul>

                    <h3><i class="fas fa-question-circle"></i> Need Help?</h3>
                    <p>If you need assistance or have questions about using the system, please contact the system administrator.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary close-modal" data-modal-id="welcome">Get Started</button>
            </div>
        </div>
    </div>

    <!-- Statistics Breakdown Modal -->
    <div id="statsBreakdownModal" class="modal large">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-chart-bar"></i> <span id="statsModalTitle">Statistics Breakdown</span></h2>
                <button type="button" class="close-modal" data-modal-id="statsBreakdown">&times;</button>
            </div>
            <div class="modal-body">
                <div id="statsBreakdownLoadingIndicator" class="loading-indicator active">
                    <i class="fas fa-spinner fa-spin"></i> Loading statistics data...
                </div>
                <div id="statsBreakdownErrorIndicator" class="error-indicator" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span>Could not load statistics data.</span>
                </div>
                <div id="statsBreakdownContent" style="display: none;">
                    <div class="stats-header">
                        <div class="stats-tabs">
                            <button class="stats-tab-btn active" data-view="table">
                                Table View
                            </button>
                            <button class="stats-tab-btn" data-view="chart">
                                Chart View
                            </button>
                        </div>
                    </div>

                    <div id="statsTableView" class="stats-view">
                        <div class="stats-table-controls">
                            <div class="stats-table-length">
                                Result per page:
                                <select id="statsTableLength">
                                    <option value="10" selected>10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <div class="stats-table-search">
                                <input type="search" id="statsTableSearch" placeholder="Search in actions, details...">
                            </div>
                        </div>
                        <div class="stats-table-container">
                            <table id="statsBreakdownTable" class="stats-table">
                                <thead>
                                    <tr>
                                        <th>NAME</th>
                                        <th>COUNT</th>
                                        <th>PERCENTAGE</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                        <div class="stats-table-pagination">
                            <div class="stats-table-info">
                                Showing <span id="statsTableStart">1</span> to <span id="statsTableEnd">10</span> of <span id="statsTableTotal">0</span> entries
                            </div>
                            <div class="stats-table-pages">
                                <button id="statsTablePrevious" class="pagination-btn" disabled>&laquo; Previous</button>
                                <div id="statsTablePageNumbers" class="pagination-numbers"></div>
                                <button id="statsTableNext" class="pagination-btn">Next &raquo;</button>
                            </div>
                        </div>
                    </div>

                    <div id="statsChartView" class="stats-view" style="display: none;">
                        <div class="chart-summary">
                            <div class="summary-item">
                                <span class="summary-label">Total:</span>
                                <span class="summary-value" id="chartTotalValue">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Top Item:</span>
                                <span class="summary-value" id="chartTopItem">None</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Items:</span>
                                <span class="summary-value" id="chartItemCount">0</span>
                            </div>
                        </div>
                        <div class="chart-type-selector" style="display: flex; justify-content: flex-end; margin-bottom: 15px;">
                            <div style="display: flex; gap: 0;">
                                <button id="doughnutChartBtn" class="chart-type-btn active" data-type="doughnut" style="padding: 6px 12px; border-radius: 4px 0 0 4px; border: 1px solid #ddd; border-right: none; background-color: var(--primary-color); color: white; cursor: pointer; font-size: 13px;">Doughnut</button>
                                <button id="barChartBtn" class="chart-type-btn" data-type="bar" style="padding: 6px 12px; border-radius: 0 4px 4px 0; border: 1px solid #ddd; background-color: #fff; color: #333; cursor: pointer; font-size: 13px;">Bar</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="statsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="statsBreakdown">Close</button>
            </div>
        </div>
    </div>


    <!-- AJAX Endpoint URL -->
    <script>
        const ajaxHandlerUrl = 'ajax_handler.php';
        const showWelcomeMessage = <?php echo $show_welcome_message ? 'true' : 'false'; ?>;
    </script>
    <!-- Include Chart.js for statistics visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <!-- Core JavaScript (Simplified - Action Bar related JS removed) -->
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        console.log("Dashboard loaded. Initializing UI (simplified).");

        // Show welcome message if this is the first page load after login
        if (showWelcomeMessage) {
            setTimeout(() => {
                openModal('welcome');
            }, 500); // Small delay to ensure everything is loaded
        }

        const notificationElement = document.getElementById('notification');
        const notificationMessageElement = document.getElementById('notificationMessage');
        const modals = {
            add: document.getElementById('addModal'), edit: document.getElementById('editModal'), delete: document.getElementById('deleteModal'), upload: document.getElementById('uploadModal'), view: document.getElementById('viewFilesModal'),
            updateProfile: document.getElementById('updateProfileModal'),
            logoutConfirm: document.getElementById('logoutConfirmModal'),
            welcome: document.getElementById('welcomeModal'),
            statsBreakdown: document.getElementById('statsBreakdownModal')
        };
        let notificationTimeout;
        let currentChart = null;

        // Global variables to store current statistic data and key
        let currentStatData = [];
        let currentStatKey = '';
        let currentPage = 1;
        let rowsPerPage = 10;
        let filteredData = [];

        function showNotification(message, type = 'success', duration = 3000) { /* ... unchanged ... */ if (!notificationElement || !notificationMessageElement) return; clearTimeout(notificationTimeout); notificationMessageElement.textContent = message; notificationElement.className = 'notification ' + type; requestAnimationFrame(() => { notificationElement.classList.add('visible'); }); notificationTimeout = setTimeout(() => { notificationElement.classList.remove('visible'); }, duration); }
        async function performAjax(action, data = {}) { /* ... unchanged ... */ const formData = new FormData(); formData.append('action', action); for (const key in data) { if (Object.prototype.hasOwnProperty.call(data, key)) { const value = data[key]; if (Array.isArray(value)) { if (key === 'ids' || key === 'file_ids') { formData.append(key, JSON.stringify(value)); } else { value.forEach(item => formData.append(key + '[]', item)); } } else if (value instanceof FileList) { for (let i = 0; i < value.length; i++) { formData.append('files[]', value[i]); } } else if (value !== null && value !== undefined) { formData.append(key, value); } } } try { const response = await fetch(ajaxHandlerUrl, { method: 'POST', body: formData }); if (!response.ok) { throw new Error(`HTTP error! Status: ${response.status}`); } const responseClone = response.clone(); let responseData; try { responseData = await response.json(); } catch (jsonError) { const errorText = await responseClone.text(); console.error('Non-JSON response:', errorText); throw new Error(`Server returned non-JSON response (Status: ${response.status}).`); } return responseData; } catch (error) { console.error('AJAX Error:', error); let userMessage = 'An unexpected error occurred.'; if (error.message.includes('non-JSON')) userMessage = 'An error occurred communicating with the server.'; else if (error.message.includes('HTTP error')) userMessage = `A server error occurred (${error.message}).`; else userMessage = `Error: ${error.message}.`; showNotification(userMessage, 'error', 5000); return { success: false, message: error.message, data: null }; } }
        function openModal(modalId) {
            const modal = modals[modalId];
            if (!modal) { console.error("Modal not found:", modalId); return; }

            resetModalContent(modalId);

            if (modalId === 'add') {
                modal.querySelector('#addForm')?.reset();
                const saveButton = modal.querySelector('#saveAddButton');
                if (saveButton) saveButton.disabled = false;
            } else if (modalId === 'updateProfile') {
                // Show loading indicator
                const loadingIndicator = modal.querySelector('#updateProfileLoadingIndicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'block';
                    loadingIndicator.classList.remove('active'); // Ensure no active class
                }
                modal.querySelector('#updateProfileErrorIndicator').style.display = 'none';
                modal.querySelector('#updateProfileForm').style.display = 'none';
                modal.querySelector('#saveProfileButton').disabled = true;

                // Fetch current user data
                performAjax('getCurrentUser')
                    .then(result => {
                        if (loadingIndicator) {
                            loadingIndicator.style.display = 'none'; // Hide loading indicator
                        }

                        if (result.success && result.data) {
                            // Populate form with user data
                            populateProfileForm(result.data);
                            modal.querySelector('#updateProfileForm').style.display = 'block';
                            modal.querySelector('#saveProfileButton').disabled = false;
                        } else {
                            // Show error
                            const errorSpan = modal.querySelector('#updateProfileErrorIndicator span');
                            if (errorSpan) errorSpan.textContent = result.message || 'Failed to load profile data.';
                            modal.querySelector('#updateProfileErrorIndicator').style.display = 'flex';
                        }
                    })
                    .catch(err => {
                        if (loadingIndicator) {
                            loadingIndicator.style.display = 'none'; // Hide loading indicator
                        }
                        const errorSpan = modal.querySelector('#updateProfileErrorIndicator span');
                        if (errorSpan) errorSpan.textContent = 'Failed to fetch profile data. Network or server error.';
                        modal.querySelector('#updateProfileErrorIndicator').style.display = 'flex';
                        console.error("Error fetching profile data:", err);
                    });
            } else if (modalId === 'welcome') {
                // Handle welcome modal animations
                const welcomeSection = modal.querySelector('.welcome-section');
                if (welcomeSection) {
                    // Remove animated class initially
                    welcomeSection.classList.remove('animated');

                    // Get all list items to animate them sequentially
                    const listItems = welcomeSection.querySelectorAll('li');

                    // Set a timeout to add the animated class after the modal appears
                    setTimeout(() => {
                        welcomeSection.classList.add('animated');

                        // Animate list items with delay
                        listItems.forEach((item, index) => {
                            setTimeout(() => {
                                item.style.transitionDelay = `${index * 100}ms`;
                            }, 500);
                        });
                    }, 300);
                }
            }

            modal.classList.add('visible');
            document.body.style.overflow = 'hidden';
        }
        function closeModal(modalId) { /* ... unchanged ... */ const modal = modals[modalId]; if (modal) { modal.classList.remove('visible'); } const anyVisible = Object.values(modals).some(m => m?.classList.contains('visible')); if (!anyVisible) { document.body.style.overflow = ''; } }
        function resetModalContent(modalId) {
            const modal = modals[modalId];
            if (!modal) return;

            if (modalId === 'edit') {
                modal.querySelector('#editForm')?.reset();
                modal.querySelector('#editModalTitle').textContent = 'Edit Activity';
                modal.querySelector('#editLoadingIndicator').style.display = 'block';
                modal.querySelector('#editErrorIndicator').style.display = 'none';
                modal.querySelector('#editForm').style.display = 'none';
                const saveBtn = modal.querySelector('#saveEditButton');
                if(saveBtn) saveBtn.disabled = true;
            } else if (modalId === 'delete') {
                const countEl = modal.querySelector('#deleteItemCount');
                if(countEl) countEl.textContent = '0';
            } else if (modalId === 'upload') {
                modal.querySelector('#uploadForm')?.reset();
                const countEl = modal.querySelector('#uploadItemCount');
                if(countEl) countEl.textContent = '0';
            } else if (modalId === 'view') {
                const countEl = modal.querySelector('#viewItemCount');
                if(countEl) countEl.textContent = '0';
                const loadingEl = modal.querySelector('#viewLoadingIndicator');
                if(loadingEl) loadingEl.style.display = 'block';
                const listEl = modal.querySelector('#fileListUl');
                if(listEl) listEl.innerHTML = '';
            } else if (modalId === 'add') {
                modal.querySelector('#addForm')?.reset();
                const saveBtn = modal.querySelector('#saveAddButton');
                if(saveBtn) {
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = 'Save Activity';
                }
            } else if (modalId === 'updateProfile') {
                modal.querySelector('#updateProfileForm')?.reset();
                const loadingIndicator = modal.querySelector('#updateProfileLoadingIndicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'block';
                    loadingIndicator.classList.remove('active'); // Remove active class to prevent continuous loading
                }
                modal.querySelector('#updateProfileErrorIndicator').style.display = 'none';
                modal.querySelector('#updateProfileForm').style.display = 'none';
                modal.querySelector('#updateProfileError').style.display = 'none';
                modal.querySelector('#updateProfileError').textContent = '';
                const saveBtn = modal.querySelector('#saveProfileButton');
                if(saveBtn) {
                    saveBtn.disabled = true;
                    saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                }
            } else if (modalId === 'welcome') {
                // Reset welcome modal animations
                const welcomeSection = modal.querySelector('.welcome-section');
                if (welcomeSection) {
                    welcomeSection.classList.remove('animated');
                    const listItems = welcomeSection.querySelectorAll('li');
                    listItems.forEach(item => {
                        item.style.transitionDelay = '0ms';
                    });
                }
            } else if (modalId === 'statsBreakdown') {
                // Reset stats breakdown modal
                const loadingIndicator = modal.querySelector('#statsBreakdownLoadingIndicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'block';
                    loadingIndicator.classList.add('active');
                }
                modal.querySelector('#statsBreakdownErrorIndicator').style.display = 'none';
                modal.querySelector('#statsBreakdownContent').style.display = 'none';
                modal.querySelector('#statsModalTitle').textContent = 'Statistics Breakdown';

                // Reset table
                const tableBody = modal.querySelector('#statsBreakdownTable tbody');
                if (tableBody) tableBody.innerHTML = '';

                // Reset chart
                if (currentChart) {
                    currentChart.destroy();
                    currentChart = null;
                }

                // Reset tabs
                const tabBtns = modal.querySelectorAll('.stats-tab-btn');
                tabBtns.forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.view === 'table');
                });

                // Reset search
                const searchInput = document.getElementById('statsTableSearch');
                if (searchInput) searchInput.value = '';

                // Reset pagination
                const lengthSelect = document.getElementById('statsTableLength');
                if (lengthSelect) lengthSelect.value = '10';
                rowsPerPage = 10;
                currentPage = 1;

                // Reset chart summary
                document.getElementById('chartTotalValue').textContent = '0';
                document.getElementById('chartTopItem').textContent = 'None';
                document.getElementById('chartItemCount').textContent = '0';

                // Show table view, hide chart view
                const tableView = modal.querySelector('#statsTableView');
                const chartView = modal.querySelector('#statsChartView');
                if (tableView) tableView.style.display = 'block';
                if (chartView) chartView.style.display = 'none';
            }
        }

        // Modal Close Handlers
        Object.keys(modals).forEach(key => {
            const modal = modals[key];
            if (modal) {
                modal.querySelectorAll('.close-modal').forEach(button => { button.addEventListener('click', () => closeModal(key)); });
                modal.addEventListener('click', (event) => { if (event.target === modal) { closeModal(key); } });
            }
        });

        // Add Form Submission (if modal is ever used)
        const addForm = document.getElementById('addForm');
        if (addForm) {
            addForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                const saveButton = document.getElementById('saveAddButton');
                saveButton.disabled = true; saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                const formData = new FormData(addForm);
                const data = Object.fromEntries(formData.entries());
                const result = await performAjax('addActivity', data);
                if (result.success) {
                    showNotification(result.message || 'Activity added successfully!', 'success'); closeModal('add'); window.location.reload();
                } else {
                    showNotification(`Error adding activity: ${result.message || 'Unknown error'}`, 'error', 5000); saveButton.disabled = false; saveButton.innerHTML = 'Save Activity';
                }
            });
        }

        // --- Settings Popup Logic ---
        const settingsBtn = document.getElementById('settingsBtn');
        const settingsMenu = document.getElementById('settingsMenuPopup');

        const logoutBtn = document.getElementById('logoutBtn');
        if (settingsBtn && settingsMenu) {
            settingsBtn.addEventListener('click', (event) => {
                event.preventDefault();
                const isVisible = settingsMenu.classList.toggle('visible');
                settingsBtn.setAttribute('aria-expanded', isVisible);
            });
            document.addEventListener('click', (event) => {
                if (settingsMenu && settingsMenu.classList.contains('visible') &&
                    !settingsBtn.contains(event.target) &&
                    !settingsMenu.contains(event.target)) {
                    settingsMenu.classList.remove('visible');
                    settingsBtn.setAttribute('aria-expanded', 'false');
                }
            });
        }

        const updateProfileBtn = document.getElementById('updateProfileBtn');
        if (updateProfileBtn) {
            updateProfileBtn.addEventListener('click', (e) => {
                e.preventDefault();
                settingsMenu?.classList.remove('visible');
                settingsBtn?.setAttribute('aria-expanded', 'false');
                openModal('updateProfile');
            });
        }

        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                settingsMenu?.classList.remove('visible');
                settingsBtn?.setAttribute('aria-expanded', 'false');
                openModal('logoutConfirm');
            });
        }

        // --- Logout Confirmation Logic ---
        const confirmLogoutButton = document.getElementById('confirmLogoutButton');
        if (confirmLogoutButton) {
            confirmLogoutButton.addEventListener('click', () => {
                confirmLogoutButton.disabled = true;
                confirmLogoutButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging out...';
                window.location.href = 'logout.php';
            });
        }

        // Function to populate profile form with user data
        function populateProfileForm(userData) {
            document.getElementById('profile_user_id').value = userData.user_id || '';
            document.getElementById('profile_username').value = userData.username || '';
            document.getElementById('profile_full_name').value = userData.full_name || '';
            document.getElementById('profile_email').value = userData.email || '';
            // Reset password fields
            document.getElementById('profile_password').value = '';
            document.getElementById('profile_confirm_password').value = '';
        }

        // Update Profile Form Submission
        const updateProfileForm = document.getElementById('updateProfileForm');
        const updateProfileErrorDiv = document.getElementById('updateProfileError');

        if (updateProfileForm && updateProfileErrorDiv) {
            updateProfileForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                updateProfileErrorDiv.style.display = 'none';
                updateProfileErrorDiv.textContent = '';

                const password = document.getElementById('profile_password').value;
                const confirmPassword = document.getElementById('profile_confirm_password').value;

                // Validate passwords if provided
                if (password && password !== confirmPassword) {
                    updateProfileErrorDiv.textContent = 'New passwords do not match.';
                    updateProfileErrorDiv.style.display = 'block';
                    return;
                }

                if (password && password.length < 8) {
                    updateProfileErrorDiv.textContent = 'New password must be at least 8 characters long.';
                    updateProfileErrorDiv.style.display = 'block';
                    return;
                }

                // Prepare form data
                const formData = new FormData(updateProfileForm);
                const data = Object.fromEntries(formData.entries());
                const saveButton = document.getElementById('saveProfileButton');

                // Disable button and show loading state
                saveButton.disabled = true;
                saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                // Submit form
                const result = await performAjax('updateProfile', data);

                if (result.success) {
                    showNotification(result.message || 'Profile updated successfully!', 'success');
                    closeModal('updateProfile');

                    // Update sidebar user information
                    if (result.data && result.data.username && result.data.full_name) {
                        const usernameElement = document.querySelector('.app-title span');
                        const fullNameElement = document.querySelector('.app-title strong');

                        if (usernameElement) usernameElement.textContent = result.data.username;
                        if (fullNameElement) fullNameElement.textContent = result.data.full_name;
                    }
                } else {
                    updateProfileErrorDiv.textContent = result.message || 'An unknown error occurred.';
                    updateProfileErrorDiv.style.display = 'block';
                    saveButton.disabled = false;
                    saveButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                }
            });
        }

        // Statistics Card Click Handlers
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            card.addEventListener('click', async function() {
                const statKey = this.querySelector('[data-key]')?.dataset.key || this.dataset.key;
                if (!statKey) {
                    console.error('No stat key found for this card');
                    return;
                }

                const statTitle = this.querySelector('h4')?.textContent || 'Statistics Breakdown';
                openStatsModal(statKey, statTitle);
            });
        });

        // Function to open stats modal and load data
        async function openStatsModal(statKey, statTitle) {
            // Store the current statistic key
            currentStatKey = statKey;

            // Reset current data
            currentStatData = [];
            currentPage = 1;

            // Reset any existing chart
            if (currentChart) {
                currentChart.destroy();
                currentChart = null;
            }

            // Open the modal
            openModal('statsBreakdown');

            // Update modal title
            const titleEl = document.querySelector('#statsModalTitle');
            if (titleEl) titleEl.textContent = statTitle;

            // Reset modal state
            resetStatsModal();

            try {
                // Fetch data from server
                const result = await performAjax('getStatBreakdown', { stat_key: statKey });

                handleStatBreakdownResult(result, statKey);
            } catch (error) {
                console.error('Error loading statistics:', error);
                document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'none';
                document.querySelector('#statsBreakdownErrorIndicator').style.display = 'block';
                document.querySelector('#statsBreakdownErrorIndicator span').textContent = 'Error loading statistics: ' + error.message;
            }
        }

        // Function to reset modal state
        function resetStatsModal() {
            // Reset tabs
            const tabBtns = document.querySelectorAll('.stats-tab-btn');
            tabBtns.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.view === 'table');
            });

            // Show table view, hide chart view
            document.getElementById('statsTableView').style.display = 'block';
            document.getElementById('statsChartView').style.display = 'none';

            // Reset search
            const searchInput = document.getElementById('statsTableSearch');
            if (searchInput) searchInput.value = '';

            // Reset pagination
            const lengthSelect = document.getElementById('statsTableLength');
            if (lengthSelect) lengthSelect.value = '10';
            rowsPerPage = 10;
            currentPage = 1;

            // Clear table
            const tableBody = document.querySelector('#statsBreakdownTable tbody');
            if (tableBody) tableBody.innerHTML = '';
        }

        // Function to handle stat breakdown result
        function handleStatBreakdownResult(result, statKey) {
            if (result.success && result.data) {
                // Hide loading indicator
                document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'none';

                // Show content container
                document.querySelector('#statsBreakdownContent').style.display = 'block';

                // Store the current data globally
                currentStatData = result.data;
                filteredData = [...currentStatData];

                // Populate table with data
                populateStatsTable(filteredData);

                // Set up tab switching
                setupStatsTabs();

                // Set up search functionality
                setupStatsSearch();

                // Set up pagination controls
                setupStatsPagination();
            } else {
                throw new Error(result.message || 'Failed to load statistics data');
            }
        }

        // Function to populate stats table
        function populateStatsTable(data) {
            const tableBody = document.querySelector('#statsBreakdownTable tbody');
            if (!tableBody) return;

            // Calculate total for percentages
            const total = data.reduce((sum, item) => sum + parseInt(item.count || 0), 0);

            // Calculate pagination
            const startIndex = (currentPage - 1) * rowsPerPage;
            const endIndex = startIndex + rowsPerPage;
            const paginatedData = data.slice(startIndex, endIndex);

            // Clear existing rows
            tableBody.innerHTML = '';

            // Add rows to table
            paginatedData.forEach(item => {
                const percentage = total > 0 ? ((item.count / total) * 100).toFixed(1) : '0.0';
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${escapeHtml(item.name || '')}</td>
                    <td>${parseInt(item.count || 0).toLocaleString()}</td>
                    <td>${percentage}%</td>
                `;
                tableBody.appendChild(row);
            });

            // Update pagination info
            updatePaginationInfo(data.length);
        }

        // Function to update pagination info
        function updatePaginationInfo(totalItems) {
            const startIndex = (currentPage - 1) * rowsPerPage + 1;
            const endIndex = Math.min(currentPage * rowsPerPage, totalItems);

            document.getElementById('statsTableStart').textContent = totalItems > 0 ? startIndex : 0;
            document.getElementById('statsTableEnd').textContent = endIndex;
            document.getElementById('statsTableTotal').textContent = totalItems;

            // Update pagination buttons
            const totalPages = Math.ceil(totalItems / rowsPerPage);
            const prevBtn = document.getElementById('statsTablePrevious');
            const nextBtn = document.getElementById('statsTableNext');
            const pageNumbersContainer = document.getElementById('statsTablePageNumbers');

            if (prevBtn) {
                prevBtn.disabled = currentPage <= 1;
            }

            if (nextBtn) {
                nextBtn.disabled = currentPage >= totalPages;
            }

            // Update page numbers
            if (pageNumbersContainer) {
                pageNumbersContainer.innerHTML = '';

                if (totalPages <= 1) return;

                // Calculate which page numbers to show
                const maxVisiblePages = 5;
                let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                // Adjust start page if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                    startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                // Add page number buttons
                for (let i = startPage; i <= endPage; i++) {
                    const pageButton = document.createElement('a');
                    pageButton.href = "#";
                    pageButton.className = i === currentPage ? 'page-number active' : 'page-number';
                    pageButton.textContent = i;
                    pageButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = i;
                        populateStatsTable(filteredData);
                    });
                    pageNumbersContainer.appendChild(pageButton);
                }
            }
        }

        // Function to set up search functionality
        function setupStatsSearch() {
            const searchInput = document.getElementById('statsTableSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    filteredData = currentStatData.filter(item =>
                        (item.name || '').toLowerCase().includes(searchTerm)
                    );
                    currentPage = 1;
                    populateStatsTable(filteredData);
                });
            }
        }

        // Function to set up pagination controls
        function setupStatsPagination() {
            const rowsPerPageSelect = document.getElementById('statsTableLength');
            const prevBtn = document.getElementById('statsTablePrevious');
            const nextBtn = document.getElementById('statsTableNext');

            if (rowsPerPageSelect) {
                rowsPerPageSelect.addEventListener('change', function() {
                    rowsPerPage = parseInt(this.value);
                    currentPage = 1;
                    populateStatsTable(filteredData);
                });
            }

            if (prevBtn) {
                prevBtn.addEventListener('click', function() {
                    if (currentPage > 1) {
                        currentPage--;
                        populateStatsTable(filteredData);
                    }
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', function() {
                    const totalPages = Math.ceil(filteredData.length / rowsPerPage);
                    if (currentPage < totalPages) {
                        currentPage++;
                        populateStatsTable(filteredData);
                    }
                });
            }
        }

        // Function to initialize chart (matching ilcdb.php exactly)
        function initializeStatsChart(data, forceChartType = null) {
            // Store data in global variable if not already set
            if (data && data.length > 0) {
                currentStatData = data;
            }

            // Update summary information
            updateChartSummary(currentStatData);

            // Determine chart type
            let chartType = forceChartType;
            if (!chartType) {
                // Get active chart type button
                const activeChartTypeBtn = document.querySelector('.chart-type-btn.active');
                if (activeChartTypeBtn) {
                    chartType = activeChartTypeBtn.dataset.type;
                } else {
                    chartType = 'doughnut'; // Default to doughnut
                }
            }

            // Prepare chart data
            const labels = currentStatData.map(item => item.name);
            const values = currentStatData.map(item => item.count);
            const backgroundColors = generateChartColors(currentStatData.length);

            // Create chart configuration
            const config = createChartConfig(chartType, labels, values, backgroundColors);

            // Get chart canvas
            const canvas = document.getElementById('statsChart');
            if (!canvas) return;

            // Destroy existing chart if it exists
            if (currentChart) {
                currentChart.destroy();
                currentChart = null;
            }

            // Create new chart
            currentChart = new Chart(canvas, config);
        }

        // Function to create chart configuration based on chart type
        function createChartConfig(chartType, labels, values, backgroundColors) {
            // Common tooltip callback
            const tooltipCallback = {
                label: function(context) {
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const value = context.raw;
                    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
                    return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
                }
            };

            // Base configuration
            const baseConfig = {
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: backgroundColors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: tooltipCallback.label
                            }
                        }
                    }
                }
            };

            // Chart type specific configurations
            if (chartType === 'doughnut') {
                return {
                    type: 'doughnut',
                    data: baseConfig.data,
                    options: {
                        ...baseConfig.options,
                        cutout: '60%',
                        plugins: {
                            ...baseConfig.options.plugins,
                            legend: {
                                position: 'right',
                                labels: {
                                    boxWidth: 15,
                                    padding: 15,
                                    font: {
                                        size: 12
                                    }
                                }
                            }
                        }
                    }
                };
            } else if (chartType === 'bar') {
                return {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Count',
                            data: values,
                            backgroundColor: backgroundColors,
                            borderWidth: 1,
                            borderColor: backgroundColors.map(color => color.replace('0.8', '1'))
                        }]
                    },
                    options: {
                        ...baseConfig.options,
                        indexAxis: labels.length > 10 ? 'y' : 'x', // Use horizontal bar for many items
                        plugins: {
                            ...baseConfig.options.plugins,
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0
                                }
                            }
                        }
                    }
                };
            }

            // Default to doughnut if unknown type
            return {
                type: 'doughnut',
                data: baseConfig.data,
                options: baseConfig.options
            };
        }

        // Function to update chart summary information
        function updateChartSummary(data) {
            const totalValue = data.reduce((sum, item) => sum + item.count, 0);
            const itemCount = data.length;

            // Find top item (item with highest count)
            let topItem = { name: 'None', count: 0 };
            if (data.length > 0) {
                topItem = data.reduce((max, item) => item.count > max.count ? item : max, data[0]);
            }

            // Update summary elements
            document.getElementById('chartTotalValue').textContent = totalValue.toLocaleString();
            document.getElementById('chartItemCount').textContent = itemCount.toLocaleString();

            // Format top item text with percentage
            const topItemPercentage = totalValue > 0 ? ((topItem.count / totalValue) * 100).toFixed(1) : '0.0';
            document.getElementById('chartTopItem').textContent = `${topItem.name} (${topItemPercentage}%)`;
        }



        // Function to generate chart colors (matching ilcdb.php exactly)
        function generateChartColors(count) {
            const colors = [
                '#6A5AE0', '#36A2EB', '#4BC0C0', '#9966FF', '#FF9F40',
                '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384', '#36A2EB',
                '#FFCD56', '#FF9F40', '#9966FF', '#C9CBCF', '#4BC0C0'
            ];

            const result = [];
            for (let i = 0; i < count; i++) {
                result.push(colors[i % colors.length]);
            }
            return result;
        }

        // Helper function to escape HTML
        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }

        // Function to set up tabs in stats modal (matching ilcdb.php exactly)
        function setupStatsTabs() {
            const tabBtns = document.querySelectorAll('.stats-tab-btn');
            const tableView = document.getElementById('statsTableView');
            const chartView = document.getElementById('statsChartView');

            // Remove existing event listeners by cloning and replacing buttons
            tabBtns.forEach(btn => {
                const newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);
            });

            // Get fresh references to the buttons
            const freshTabBtns = document.querySelectorAll('.stats-tab-btn');

            freshTabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const view = btn.dataset.view;

                    // Update active tab
                    freshTabBtns.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    // Show/hide views
                    if (view === 'table') {
                        if (tableView) tableView.style.display = 'block';
                        if (chartView) chartView.style.display = 'none';
                    } else if (view === 'chart') {
                        if (tableView) tableView.style.display = 'none';
                        if (chartView) chartView.style.display = 'block';
                        // Initialize chart when switching to chart view
                        initializeStatsChart(currentStatData);
                    }
                });
            });

            // Set up chart type buttons
            setupChartTypeButtons();
        }

        // Function to set up chart type buttons (matching ilcdb.php exactly)
        function setupChartTypeButtons() {
            const chartTypeBtns = document.querySelectorAll('.chart-type-btn');

            // Remove existing event listeners by cloning and replacing buttons
            chartTypeBtns.forEach(btn => {
                const newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);
            });

            // Get fresh references to the buttons
            const freshChartTypeBtns = document.querySelectorAll('.chart-type-btn');

            freshChartTypeBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    // Skip if already active
                    if (btn.classList.contains('active')) return;

                    // Update active state for all buttons
                    freshChartTypeBtns.forEach(b => {
                        b.classList.remove('active');
                        b.style.backgroundColor = '#fff';
                        b.style.color = '#333';
                    });

                    // Set active state for clicked button
                    btn.classList.add('active');
                    btn.style.backgroundColor = 'var(--primary-color)';
                    btn.style.color = 'white';

                    // Get selected chart type
                    const selectedChartType = btn.dataset.type;

                    // Update the chart with the new type
                    initializeStatsChart(currentStatData, selectedChartType);
                });
            });
        }
      }); // End DOMContentLoaded
    </script>

    <?php
      // Close DB connection
      if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
          mysqli_close($conn);
      }
    ?>
</body>
</html>